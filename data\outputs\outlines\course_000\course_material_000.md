# 函数基础与应用

## 函数定义与结构
### 函数是带名字的代码块，用于完成具体任务，并通过调用来执行。
### 使用 def 关键字定义函数，并指定函数名及必要信息的括号。
### 函数体由缩进行组成，通常包含文档字符串说明功能。
### 调用函数时只需输入函数名和括号，Python执行其中的代码。
### 函数的使用提高了程序的编写、阅读、测试和修复效率。

## 传递参数方式
### 可以通过在函数定义括号内添加形参，让函数接受外部信息。
### 调用函数时将实参传递给形参，实现个性化的输出。
### 形参是函数完成任务所需的信息，实参是在调用时提供的数据。
### 位置实参要求实参顺序与形参一致，否则结果可能异常。
### 关键字实参通过名值对传递，可以避免顺序混淆并提高可读性。
### 默认值允许省略某些实参，简化调用并指出常用方式。

## 实参传递方法
### 位置实参按顺序关联，顺序错误会导致意外结果。
### 关键字实参直接指定形参名，顺序无关紧要但需名称准确。
### 默认值允许省略部分实参，未指定时自动使用默认值。
### 混合使用位置实参、关键字实参和默认值可实现多种等效调用方式。
### 实参数量不匹配会导致错误，Python会给出详细的 traceback 信息。

## 返回值及应用
### 函数可通过 return 语句返回一个或多个值，简化主程序并提高复用性。
### 返回值可赋给变量，用于后续处理或显示。
### 可以通过默认值让某些实参变为可选，适应不同调用需求。
### 函数不仅能返回简单值，还能返回复杂数据结构如字典或列表。
### 可根据需要扩展函数，支持额外信息如中间名、年龄等。

## 函数与控制结构结合
### 函数可与 while 循环结合，实现持续交互并提供退出条件。
### 向函数传递列表可批量处理数据，通过遍历实现高效操作。
### 在函数中修改传入的列表会永久影响原始数据，适合数据处理场景。
### 可通过传递列表副本禁止函数修改原始列表，避免数据丢失。
### 每个函数应只负责一项具体工作，便于维护和扩展。

## 任意数量实参接收
### 通过 *args 语法，函数可接收任意数量的位置实参并封装为元组。
### 通过 **kwargs 语法，函数可接收任意数量的关键字实参并封装为字典。
### 接收不同类型的实参时，需将 *args 放在其他形参之后。
### 混合使用位置、关键字和任意数量实参可灵活应对各种调用需求。
### 这种机制常用于参数不确定或扩展性强的函数，如制作比萨或用户信息。

## 模块中的函数管理
### 可将函数存储在独立的 .py 文件（模块）中，主程序通过 import 导入使用。
### 导入整个模块后，通过模块名.函数名调用其中函数。
### 可只导入特定函数，调用时无需点号，代码更简洁。
### 可以用 as 给函数或模块指定别名，避免名称冲突，提升代码可读性。
### 使用 from module import * 可导入模块所有函数，但不建议在大型项目中使用。
### 导入函数和模块有多种方法，应根据项目需求选择最清晰的方式。

## 函数与模块规范
### 函数和模块名称应描述性强，仅用小写字母和下划线。
### 每个函数应有文档字符串，简要说明其功能和用法。
### 形参默认值的等号两侧不应有空格，符合 PEP 8 的代码风格。
### 代码行长度不超过 79 字符，参数过多时可分行并适当缩进。
### 相邻函数间用两个空行分隔，import 语句应放在文件开头。

## 函数优势与目标
### 函数让代码复用变得简单，只需一行调用即可执行复杂任务。
### 修改函数行为时只需更改一个代码块，影响所有调用处。
### 良好的函数名提高程序可读性，帮助快速理解程序结构。
### 函数让测试和调试更加容易，每个函数单独测试确保稳定性。
### 编写函数有助于实现简单高效的编程目标，让开发更专注于核心任务。