# AI聊天组件UI设计文档

## 整体布局

### 容器设计
- **位置**：页面右侧固定位置
- **尺寸**：宽度20%，最小320px，最大400px，高度100vh
- **外观**：圆角卡片设计，带有阴影效果
- **背景**：纯白色卡片背景
- **边框**：浅灰色细边框

### 响应式设计
- 移动端：全屏显示，去除侧边距
- 桌面端：右侧固定宽度显示

## 配色方案

### 主色调
- **主色**：蓝色系 (oklch(0.5500 0.1800 230.0000))
- **背景色**：浅灰白色 (oklch(0.9900 0.0020 106.0000))
- **卡片背景**：纯白色
- **文字主色**：深灰色 (oklch(0.2000 0.0050 264.0000))

### 功能色彩
- **危险操作**：橙红色 (oklch(0.6200 0.2000 25.0000))
- **次要信息**：中灰色 (oklch(0.5000 0.0100 264.0000))
- **边框色**：浅灰色 (oklch(0.9200 0.0100 220.0000))
- **输入框背景**：极浅灰色 (oklch(0.9800 0.0050 220.0000))

## 组件结构

### 1. 调试区域（顶部）
**功能**：可折叠的调试面板
- **标题栏**：
  - 左侧：设置图标 + "调试模式"文字
  - 右侧：下拉箭头图标
  - 背景：浅灰色，悬停时变为强调色
- **内容区**：
  - 两个并排输入框："Conservation ID" 和 "Course ID"
  - 输入框样式：圆角、浅色背景、聚焦时蓝色边框
- **交互**：点击标题栏可折叠/展开，带有平滑动画

### 2. 聊天记录区域（中部）
**功能**：显示对话历史
- **头部**：
  - 右侧：清除对话按钮（垃圾桶图标 + "清除对话"文字）
  - 按钮样式：透明背景、边框、悬停时变红色
- **消息列表**：
  - 滚动区域，自定义滚动条（4px宽度，圆角）
  - 消息间距：16px

#### 消息样式
**AI消息**：
- 头像：圆形，蓝色背景，白色"AI"文字
- 内容：浅灰色背景，圆角气泡
- 位置：左对齐
- 时间：左对齐，小字体

**用户消息**：
- 头像：圆形，浅色背景，"用"字
- 内容：蓝色背景，白色文字，圆角气泡
- 位置：右对齐
- 时间：右对齐，小字体

**打字指示器**：
- 三个跳动的小圆点
- 动画：依次放大缩小，循环播放

### 3. 输入区域（底部）
**功能**：消息输入和发送

#### 输入框
- **样式**：多行文本框，自动调整高度
- **最大高度**：10行文本
- **占位符**："输入您的问题..."
- **边框**：聚焦时蓝色高亮
- **滚动条**：超出最大高度时显示

#### 控制栏
- **左侧**：模式选择下拉框
  - 选项："根据课件" / "通用知识"
  - 样式：小尺寸，圆角边框
- **右侧**：发送按钮
  - 图标：发送箭头
  - 样式：蓝色背景，白色文字和图标
  - 悬停效果：轻微放大 + 阴影

## 交互动效

### 页面加载
- 整个容器从下方淡入上升

### 消息动画
- AI消息：从左侧滑入
- 用户消息：从右侧滑入
- 消息悬停：轻微上移

### 按钮交互
- 发送按钮：悬停放大，点击缩小
- 清除按钮：悬停变红，垃圾桶图标摇摆
- 调试折叠：箭头旋转180度

### 确认弹窗
- 背景：半透明黑色遮罩
- 弹窗：从上方滑入，白色卡片
- 按钮：取消（灰色）、确认（红色）

## 字体规范

### 字体族
- **主字体**：系统字体栈（苹方、微软雅黑等）
- **等宽字体**：JetBrains Mono（用于代码）

### 字体大小
- **标题**：16px（弹窗标题）
- **正文**：14px（消息内容）
- **小字**：12px（调试标签、按钮文字）
- **微字**：11px（时间戳）

## 间距规范

### 内边距
- **容器内边距**：16px
- **消息内边距**：8px 12px
- **按钮内边距**：6px 12px（小按钮）、8px 16px（主按钮）

### 外边距
- **消息间距**：16px
- **组件间距**：12px
- **元素间距**：6-8px

## 圆角规范
- **主圆角**：8px（容器、弹窗）
- **小圆角**：4px（按钮、输入框）
- **头像圆角**：50%（完全圆形）

## 阴影效果
- **容器阴影**：大阴影，营造悬浮感
- **按钮悬停阴影**：中等阴影
- **弹窗阴影**：超大阴影，突出层级

## 设计特点

### 现代化设计
- 采用卡片式布局，层次分明
- 圆角设计，视觉柔和
- 适度的阴影效果，增强立体感

### 用户体验
- 清晰的视觉层级
- 直观的交互反馈
- 流畅的动画过渡
- 响应式适配

### 色彩心理
- 蓝色主色调：专业、可信赖
- 浅色背景：简洁、清爽
- 红色警告：明确的危险提示

## 技术实现建议

### CSS变量系统
- 使用CSS自定义属性管理颜色
- 便于主题切换和维护
- 支持OKLCH颜色空间

### 动画性能
- 使用transform和opacity进行动画
- 避免引起重排重绘的属性
- 合理使用GPU加速

### 无障碍设计
- 保证足够的颜色对比度
- 支持键盘导航
- 提供适当的焦点指示

这个设计采用了现代化的卡片式布局，配色清新简洁，交互流畅自然，适合作为AI聊天功能的用户界面。整体风格偏向简约专业，符合现代Web应用的设计趋势。
