# 前端独立部署说明

这是从 FastAPI 项目中分离出来的独立前端应用。现在前端和后端完全分离，可以独立部署和开发。

## 功能特色

### 🎯 核心功能模块

#### 1. 文档处理

- **大纲生成**: 上传 Markdown/文本文档，自动生成结构化大纲
- **任务管理**: 查看和管理所有大纲生成任务
- **性能监控**: 实时查看系统性能指标和统计信息

#### 2. RAG 检索

- **智能问答**: 基于已索引文档的智能问答系统
- **索引管理**: 建立和管理文档向量索引
- **集合管理**: 管理 Qdrant 向量数据库集合

#### 3. 课程材料

- **统一处理**: 一站式文件处理（大纲生成 + RAG 索引）
- **材料管理**: 查看和管理已上传的课程材料
- **清理工具**: 清理指定材料或整个课程的数据

#### 4. 系统监控

- **健康状态**: 实时监控系统各组件状态
- **API 文档**: 直接访问 Swagger/ReDoc 文档

### 🎨 界面特色

- **响应式设计**: 支持桌面端和移动端
- **暗色主题**: 支持亮色/暗色主题切换
- **实时反馈**: 任务进度实时更新
- **拖拽上传**: 支持文件拖拽上传
- **Markdown 渲染**: 实时渲染 Markdown 内容

## 使用指南

### 启动服务

1. 确保 FastAPI 后端服务正在运行
2. 访问 `http://localhost:8000/static/index.html`

### 大纲生成

1. 点击侧边栏的"大纲生成"
2. 拖拽或选择 .md/.txt 文件
3. 填写课程信息（课程 ID、材料 ID、材料名称）
4. 点击"生成大纲"开始处理
5. 实时查看任务状态和进度
6. 完成后可预览、复制或下载大纲

### 智能问答

1. 点击侧边栏的"智能问答"
2. 在右侧设置面板配置查询参数
3. 在聊天框中输入问题
4. 查看 AI 回答和相关来源信息
5. 支持连续对话和上下文记忆

### 统一处理

1. 点击侧边栏的"统一处理"
2. 上传课程材料文件
3. 配置 RAG 索引选项
4. 一键完成文件处理、大纲生成和索引建立
5. 查看完整的处理结果

### 集合管理

1. 点击侧边栏的"集合管理"
2. 查看所有向量数据库集合
3. 点击"详情"查看集合信息
4. 可删除不需要的集合

### 系统监控

1. 点击侧边栏的"健康状态"
2. 查看系统各组件运行状态
3. 使用快速检查按钮测试各 API
4. 监控系统概览信息

## 技术架构

### 前端技术栈

- **HTML5 + CSS3 + JavaScript (ES6+)**
- **Bootstrap 5**: UI 组件库
- **Bootstrap Icons**: 图标库
- **Marked.js**: Markdown 渲染
- **Chart.js**: 图表库（预留）

### 核心特性

- **模块化设计**: 代码分离，易于维护
- **异步处理**: 基于 Promise/async-await
- **错误处理**: 完善的错误提示和处理
- **状态管理**: 全局应用状态管理
- **实时轮询**: 任务状态实时更新

### 文件结构

```
app/static/
├── index.html              # 主页面
├── css/
│   └── style.css          # 样式文件
├── js/
│   ├── app.js             # 主应用逻辑
│   ├── api.js             # API 调用模块
│   ├── pages.js           # 页面内容模块
│   └── pages-extended.js  # 扩展页面功能
└── README.md              # 说明文档
```

## API 集成

界面完全基于现有的 FastAPI 后端 API，包括：

- `/api/v1/outline/*` - 大纲生成相关
- `/api/v1/rag/*` - RAG 检索相关
- `/api/v1/course-materials/*` - 课程材料处理
- `/health` - 系统健康检查

## 浏览器兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 开发说明

### 添加新功能

1. 在 `js/api.js` 中添加 API 调用方法
2. 在 `js/pages.js` 或 `js/pages-extended.js` 中添加页面逻辑
3. 在 `css/style.css` 中添加样式
4. 在 `index.html` 中添加导航链接

### 自定义样式

可以通过修改 `css/style.css` 中的 CSS 变量来自定义主题：

```css
:root {
  --primary-color: #0d6efd;
  --secondary-color: #6c757d;
  --success-color: #198754;
  /* ... 更多变量 */
}
```

### 调试模式

在浏览器开发者工具中可以查看：

- 网络请求和响应
- 控制台日志信息
- 应用状态变化

## 注意事项

1. **文件大小限制**: 上传文件最大 10MB
2. **支持格式**: 仅支持 .md 和 .txt 格式
3. **网络要求**: 需要稳定的网络连接
4. **浏览器存储**: 使用 localStorage 保存主题设置

## 故障排除

### 常见问题

1. **页面无法加载**: 检查后端服务是否正常运行
2. **文件上传失败**: 检查文件格式和大小
3. **API 调用失败**: 检查网络连接和后端状态
4. **样式显示异常**: 清除浏览器缓存

### 获取帮助

- 查看浏览器控制台错误信息
- 检查后端日志
- 使用"健康状态"页面诊断系统问题

## 更新日志

### v1.0.0 (2024-01-16)

- 初始版本发布
- 完整的 API 可视化界面
- 支持所有核心功能模块
- 响应式设计和主题切换
