<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>对话API测试页面</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="alert alert-info">
                    <h4><i class="bi bi-info-circle"></i> 对话API测试页面</h4>
                    <p class="mb-0">专门用于测试对话相关的API端点，包括清除会话和获取会话状态功能。</p>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-gear"></i> 测试配置</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="test-conversation-id" class="form-label">会话ID</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="test-conversation-id" 
                                       placeholder="输入会话ID" value="test_conversation_123">
                                <button class="btn btn-outline-secondary" onclick="generateTestConversationId()">
                                    <i class="bi bi-arrow-clockwise"></i> 生成
                                </button>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="test-debug-mode" checked>
                                <label class="form-check-label" for="test-debug-mode">
                                    调试模式（显示详细日志）
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button class="btn btn-danger" onclick="testClearConversationAPI()">
                                <i class="bi bi-trash3"></i> 测试清除会话API
                            </button>
                            <button class="btn btn-primary" onclick="testGetConversationStatusAPI()">
                                <i class="bi bi-info-circle"></i> 测试获取会话状态API
                            </button>
                            <button class="btn btn-success" onclick="testConversationConfigAPI()">
                                <i class="bi bi-gear"></i> 测试获取配置API
                            </button>
                            <button class="btn btn-warning" onclick="testConversationHealthAPI()">
                                <i class="bi bi-heart-pulse"></i> 测试健康检查API
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="bi bi-terminal"></i> 测试结果</h5>
                        <button class="btn btn-outline-secondary btn-sm" onclick="clearTestResults()">
                            <i class="bi bi-eraser"></i> 清空
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="test-results" style="height: 400px; overflow-y: auto; font-family: monospace; font-size: 0.9em;">
                            <div class="text-muted text-center">
                                <i class="bi bi-play-circle" style="font-size: 2rem;"></i>
                                <p class="mt-2">点击左侧按钮开始测试</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-book"></i> API端点说明</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>端点</th>
                                        <th>方法</th>
                                        <th>功能</th>
                                        <th>参数</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><code>/api/v1/conversation/conversations/{id}</code></td>
                                        <td><span class="badge bg-danger">DELETE</span></td>
                                        <td>清除会话</td>
                                        <td>conversation_id (路径参数)</td>
                                    </tr>
                                    <tr>
                                        <td><code>/api/v1/conversation/conversations/{id}/status</code></td>
                                        <td><span class="badge bg-primary">GET</span></td>
                                        <td>获取会话状态</td>
                                        <td>conversation_id (路径参数)</td>
                                    </tr>
                                    <tr>
                                        <td><code>/api/v1/conversation/config</code></td>
                                        <td><span class="badge bg-success">GET</span></td>
                                        <td>获取配置信息</td>
                                        <td>无</td>
                                    </tr>
                                    <tr>
                                        <td><code>/api/v1/conversation/health</code></td>
                                        <td><span class="badge bg-warning">GET</span></td>
                                        <td>健康检查</td>
                                        <td>无</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- API JS -->
    <script src="js/api.js"></script>
    <!-- App JS -->
    <script src="js/app.js"></script>
    
    <script>
        // 生成测试会话ID
        function generateTestConversationId() {
            const timestamp = Date.now();
            const random = Math.random().toString(36).substring(2, 8);
            const conversationId = `test_${timestamp}_${random}`;
            document.getElementById('test-conversation-id').value = conversationId;
            addTestResult(`🔄 生成新的测试会话ID: ${conversationId}`, 'info');
        }
        
        // 添加测试结果
        function addTestResult(message, type = 'info') {
            const resultsContainer = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            
            // 如果是第一条消息，清空初始提示
            if (resultsContainer.querySelector('.text-muted')) {
                resultsContainer.innerHTML = '';
            }
            
            const typeColors = {
                'info': 'text-info',
                'success': 'text-success', 
                'error': 'text-danger',
                'warning': 'text-warning'
            };
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `mb-2 ${typeColors[type] || 'text-info'}`;
            resultDiv.innerHTML = `<small class="text-muted">[${timestamp}]</small> ${message}`;
            
            resultsContainer.appendChild(resultDiv);
            resultsContainer.scrollTop = resultsContainer.scrollHeight;
        }
        
        // 清空测试结果
        function clearTestResults() {
            document.getElementById('test-results').innerHTML = `
                <div class="text-muted text-center">
                    <i class="bi bi-play-circle" style="font-size: 2rem;"></i>
                    <p class="mt-2">点击左侧按钮开始测试</p>
                </div>
            `;
        }
        
        // 测试清除会话API
        async function testClearConversationAPI() {
            const conversationId = document.getElementById('test-conversation-id').value;
            const debugMode = document.getElementById('test-debug-mode').checked;
            
            if (!conversationId.trim()) {
                addTestResult('❌ 请输入会话ID', 'error');
                return;
            }
            
            try {
                addTestResult(`🧪 开始测试清除会话API - 会话ID: ${conversationId}`, 'info');
                
                if (debugMode) {
                    addTestResult(`📤 请求: DELETE /api/v1/conversation/conversations/${conversationId}`, 'info');
                }
                
                const startTime = Date.now();
                const response = await ChatAPI.clearConversation(conversationId);
                const duration = Date.now() - startTime;
                
                addTestResult(`✅ 清除会话API测试成功 (${duration}ms)`, 'success');
                addTestResult(`📋 响应: ${JSON.stringify(response, null, 2)}`, 'success');
                
                if (debugMode) {
                    console.log('清除会话API测试详情:', {
                        url: `/api/v1/conversation/conversations/${conversationId}`,
                        method: 'DELETE',
                        duration: duration + 'ms',
                        response: response
                    });
                }
                
            } catch (error) {
                addTestResult(`❌ 清除会话API测试失败: ${error.message}`, 'error');
                
                if (debugMode) {
                    console.error('清除会话API错误:', error);
                }
            }
        }
        
        // 测试获取会话状态API
        async function testGetConversationStatusAPI() {
            const conversationId = document.getElementById('test-conversation-id').value;
            const debugMode = document.getElementById('test-debug-mode').checked;
            
            if (!conversationId.trim()) {
                addTestResult('❌ 请输入会话ID', 'error');
                return;
            }
            
            try {
                addTestResult(`🧪 开始测试获取会话状态API - 会话ID: ${conversationId}`, 'info');
                
                if (debugMode) {
                    addTestResult(`📤 请求: GET /api/v1/conversation/conversations/${conversationId}/status`, 'info');
                }
                
                const startTime = Date.now();
                const response = await ChatAPI.getConversationStatus(conversationId);
                const duration = Date.now() - startTime;
                
                addTestResult(`✅ 获取会话状态API测试成功 (${duration}ms)`, 'success');
                addTestResult(`📋 响应: ${JSON.stringify(response, null, 2)}`, 'success');
                
                if (debugMode) {
                    console.log('获取会话状态API测试详情:', {
                        url: `/api/v1/conversation/conversations/${conversationId}/status`,
                        method: 'GET',
                        duration: duration + 'ms',
                        response: response
                    });
                }
                
            } catch (error) {
                addTestResult(`❌ 获取会话状态API测试失败: ${error.message}`, 'error');
                
                if (debugMode) {
                    console.error('获取会话状态API错误:', error);
                }
            }
        }
        
        // 测试获取配置API
        async function testConversationConfigAPI() {
            const debugMode = document.getElementById('test-debug-mode').checked;
            
            try {
                addTestResult(`🧪 开始测试获取配置API`, 'info');
                
                if (debugMode) {
                    addTestResult(`📤 请求: GET /api/v1/conversation/config`, 'info');
                }
                
                const startTime = Date.now();
                const response = await ChatAPI.getConfig();
                const duration = Date.now() - startTime;
                
                addTestResult(`✅ 获取配置API测试成功 (${duration}ms)`, 'success');
                addTestResult(`📋 响应: ${JSON.stringify(response, null, 2)}`, 'success');
                
                if (debugMode) {
                    console.log('获取配置API测试详情:', {
                        url: '/api/v1/conversation/config',
                        method: 'GET',
                        duration: duration + 'ms',
                        response: response
                    });
                }
                
            } catch (error) {
                addTestResult(`❌ 获取配置API测试失败: ${error.message}`, 'error');
                
                if (debugMode) {
                    console.error('获取配置API错误:', error);
                }
            }
        }
        
        // 测试健康检查API
        async function testConversationHealthAPI() {
            const debugMode = document.getElementById('test-debug-mode').checked;
            
            try {
                addTestResult(`🧪 开始测试健康检查API`, 'info');
                
                if (debugMode) {
                    addTestResult(`📤 请求: GET /api/v1/conversation/health`, 'info');
                }
                
                const startTime = Date.now();
                const response = await ChatAPI.getHealth();
                const duration = Date.now() - startTime;
                
                addTestResult(`✅ 健康检查API测试成功 (${duration}ms)`, 'success');
                addTestResult(`📋 响应: ${JSON.stringify(response, null, 2)}`, 'success');
                
                if (debugMode) {
                    console.log('健康检查API测试详情:', {
                        url: '/api/v1/conversation/health',
                        method: 'GET',
                        duration: duration + 'ms',
                        response: response
                    });
                }
                
            } catch (error) {
                addTestResult(`❌ 健康检查API测试失败: ${error.message}`, 'error');
                
                if (debugMode) {
                    console.error('健康检查API错误:', error);
                }
            }
        }
        
        // 页面加载完成后自动生成测试会话ID
        document.addEventListener('DOMContentLoaded', function() {
            addTestResult('🚀 对话API测试页面已加载', 'info');
            addTestResult('💡 提示: 可以点击"生成"按钮创建新的测试会话ID', 'info');
        });
    </script>
</body>
</html>
